export default function GameHead() {
    console.log('GameHead component rendering'); // 调试日志

    return (
        <div className="text-white p-6 text-center border-4 border-red-500" style={{ backgroundColor: '#8B5A2B' }}>
            <h1 className="text-[clamp(1.5rem,3vw,2.5rem)] font-bold">五子棋</h1>
            <p className="mt-2" style={{ color: '#D2B48C' }}>
                经典对弈游戏
            </p>
            <div className="mt-4 text-sm">
                <div className="p-2 rounded" style={{ backgroundColor: '#D2B48C' }}>
                    Secondary Color Test
                </div>
                <div className="p-2 rounded mt-2" style={{ backgroundColor: '#DEB887' }}>
                    Board Color Test
                </div>
            </div>
        </div>
    );
}
