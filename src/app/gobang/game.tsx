'use client';
import { useRef, useState } from 'react';
import { BOARD_SIZE, GameContext, GameStateContext, STARTER_PLAYER } from './context';
import GameController from './game.controller';
import GameFooter from './game.footer';
import GameHead from './game.head';
import GameMain from './game.main';
import './gobang.css';

export default function Game() {
    const [gameBoard, setGameBoard] = useState(
        Array(BOARD_SIZE)
            .fill([])
            .map(() => Array(BOARD_SIZE).fill(-1)),
    );
    //当前玩家  1-黑棋  0-白棋
    const [currentPlayer, setCurrentPlayer] = useState(STARTER_PLAYER);
    //游戏是否进行中
    const [gameActive, setGameActive] = useState(true);
    //
    const [winner, setWinner] = useState(undefined);
    //
    const [moveHistory, setMoveHistory] = useState([]);

    const [gameTime, setGameTime] = useState(0);

    const canvasRef = useRef(null);
    const timeRef = useRef(null);
    /**
     * 初始化
     */
    function initGame() {}

    /**
     *
     */
    function drawBoard() {}

    /**
     * 检查胜利条件
     */
    function checkWin() {}

    /**
     * 检查平局
     */
    function checkDraw() {}

    /**
     * 下棋
     */
    function handleCanvasClick() {}

    /**
     * 鼠标在棋盘移动
     */
    function handleCanvasMouseMove() {}

    /**
     * 鼠标离开棋盘
     */
    function handleCanvasMouseLeave() {}
    /**
     * 重新开始
     */
    function handleRestart() {}

    /**
     * 悔棋
     */
    function handleUndo() {}

    return (
        <div className="max-w-4xl w-full bg-white rounded-2xl shadow-xl overflow-hidden">
            <GameHead />
            <GameContext.Provider value={{ currentPlayer: 1, moveHistory: [] }}>
                <GameStateContext.Provider value={{ gameActive: false, winner: undefined }}>
                    <div className="p-6 md:p-8 flex flex-col md:flex-row gap-6">
                        <GameMain />
                        <GameController />
                    </div>
                </GameStateContext.Provider>
            </GameContext.Provider>
            <GameFooter />
        </div>
    );
}
