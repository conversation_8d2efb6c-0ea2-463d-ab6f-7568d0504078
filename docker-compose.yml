version: '3.8'

services:
  # 五子棋游戏应用
  gobang-game:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "80:3100"  # 映射到服务器的 80 端口
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
      - PORT=3100
      - HOSTNAME=0.0.0.0
    restart: unless-stopped
    container_name: gobang-game-prod
    networks:
      - gobang-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3100"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.gobang.rule=Host(`your-domain.com`)"
      - "traefik.http.routers.gobang.entrypoints=websecure"
      - "traefik.http.routers.gobang.tls.certresolver=letsencrypt"
      - "traefik.http.services.gobang.loadbalancer.server.port=3100"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx 反向代理（可选）
  nginx:
    image: nginx:alpine
    ports:
      - "443:443"
      - "8080:80"  # 备用端口
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - gobang-game
    restart: unless-stopped
    container_name: gobang-nginx
    networks:
      - gobang-network
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "3"

networks:
  gobang-network:
    driver: bridge

volumes:
  ssl-certs:
    driver: local
