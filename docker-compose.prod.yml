version: '3.8'

services:
  # 五子棋游戏应用
  gobang-game:
    image: gobang-game:latest  # 使用预构建的镜像
    ports:
      - "80:3100"  # 映射到服务器的 80 端口
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
      - PORT=3100
      - HOSTNAME=0.0.0.0
    restart: unless-stopped
    container_name: gobang-game-prod
    networks:
      - gobang-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3100"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /var/cache/nginx

networks:
  gobang-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
