#!/bin/bash

# 五子棋游戏服务器部署脚本
# 使用方法: ./deploy.sh [环境]
# 环境选项: dev, prod

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 和 Docker Compose
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 构建镜像
build_image() {
    log_info "构建 Docker 镜像..."
    docker build -t gobang-game:latest .
    log_success "镜像构建完成"
}

# 部署开发环境
deploy_dev() {
    log_info "部署开发环境..."
    docker-compose down --remove-orphans
    docker-compose up -d --build
    log_success "开发环境部署完成"
    log_info "访问地址: http://localhost:3100"
}

# 部署生产环境
deploy_prod() {
    log_info "部署生产环境..."
    
    # 停止现有容器
    docker-compose -f docker-compose.prod.yml down --remove-orphans
    
    # 构建镜像
    build_image
    
    # 启动生产环境
    docker-compose -f docker-compose.prod.yml up -d
    
    log_success "生产环境部署完成"
    log_info "访问地址: http://your-server-ip"
}

# 显示状态
show_status() {
    log_info "容器状态:"
    docker ps --filter "name=gobang" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    echo ""
    log_info "日志查看命令:"
    echo "  开发环境: docker-compose logs -f"
    echo "  生产环境: docker-compose -f docker-compose.prod.yml logs -f"
}

# 清理资源
cleanup() {
    log_info "清理未使用的 Docker 资源..."
    docker system prune -f
    docker image prune -f
    log_success "清理完成"
}

# 主函数
main() {
    local env=${1:-dev}
    
    echo "🎮 五子棋游戏部署脚本"
    echo "======================="
    
    check_dependencies
    
    case $env in
        "dev")
            deploy_dev
            ;;
        "prod")
            deploy_prod
            ;;
        "status")
            show_status
            return
            ;;
        "cleanup")
            cleanup
            return
            ;;
        *)
            log_error "未知环境: $env"
            echo "使用方法: $0 [dev|prod|status|cleanup]"
            exit 1
            ;;
    esac
    
    echo ""
    show_status
    
    echo ""
    log_success "部署完成! 🎉"
}

# 执行主函数
main "$@"
